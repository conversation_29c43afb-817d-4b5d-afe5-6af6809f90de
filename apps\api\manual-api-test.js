/**
 * Manual API Testing Script for Freela Syria
 * Tests core API endpoints to verify functionality
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api/v1';
let authToken = null;

// Test data
const testUser = {
  email: `test.${Date.now()}@freela.sy`,
  password: 'TestPass123!',
  firstName: 'أحمد',
  lastName: 'محمد',
  phone: '+963-11-1234567',
  role: 'EXPERT',
  language: 'ar',
  acceptTerms: true
};

const existingUser = {
  email: '<EMAIL>',
  password: 'password123'
};

async function testEndpoint(name, method, url, data = null, headers = {}) {
  try {
    console.log(`\n🧪 Testing: ${name}`);
    console.log(`   ${method.toUpperCase()} ${url}`);
    
    const config = {
      method: method.toLowerCase(),
      url: `${API_BASE}${url}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`   ✅ Status: ${response.status}`);
    console.log(`   📄 Response: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
    
    return response.data;
  } catch (error) {
    console.log(`   ❌ Error: ${error.response?.status || 'Network Error'}`);
    console.log(`   📄 Message: ${error.response?.data?.message || error.message}`);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Freela Syria API Manual Tests\n');
  console.log('=' .repeat(60));
  
  // Test 1: Health Check
  await testEndpoint(
    'Health Check',
    'GET',
    '/../../health'
  );
  
  // Test 2: API Info
  await testEndpoint(
    'API Info',
    'GET',
    ''
  );
  
  // Test 3: User Registration
  const registerResult = await testEndpoint(
    'User Registration',
    'POST',
    '/auth/register',
    testUser
  );
  
  // Test 4: User Login (existing user)
  const loginResult = await testEndpoint(
    'User Login',
    'POST',
    '/auth/login',
    existingUser
  );
  
  if (loginResult?.data?.tokens?.accessToken) {
    authToken = loginResult.data.tokens.accessToken;
    console.log(`   🔑 Auth token obtained: ${authToken.substring(0, 20)}...`);
  }
  
  // Test 5: Get User Profile (authenticated)
  if (authToken) {
    await testEndpoint(
      'Get User Profile',
      'GET',
      '/auth/profile',
      null,
      { 'Authorization': `Bearer ${authToken}` }
    );
  }
  
  // Test 6: Get Services
  await testEndpoint(
    'Get Services',
    'GET',
    '/services?page=1&limit=5'
  );
  
  // Test 7: Get Experts
  await testEndpoint(
    'Get Experts',
    'GET',
    '/experts?page=1&limit=5'
  );
  
  // Test 8: Search Services
  await testEndpoint(
    'Search Services',
    'GET',
    '/search?q=development&type=services'
  );
  
  // Test 9: Get Search Filters
  await testEndpoint(
    'Get Search Filters',
    'GET',
    '/search/filters'
  );
  
  // Test 10: Create Service (authenticated)
  if (authToken) {
    const serviceData = {
      title: {
        en: 'Test Web Development Service',
        ar: 'خدمة تطوير مواقع ويب تجريبية'
      },
      description: {
        en: 'Professional web development service for testing',
        ar: 'خدمة تطوير مواقع ويب احترافية للاختبار'
      },
      category: 'tech-development',
      subcategory: 'Web Development',
      tags: ['React', 'Node.js', 'Arabic'],
      priceType: 'FIXED',
      basePrice: 500,
      deliveryTime: 7,
      revisions: 3,
      serviceType: 'DIGITAL',
      isActive: true
    };
    
    await testEndpoint(
      'Create Service',
      'POST',
      '/services',
      serviceData,
      { 'Authorization': `Bearer ${authToken}` }
    );
  }
  
  // Test 11: Get Bookings (authenticated)
  if (authToken) {
    await testEndpoint(
      'Get User Bookings',
      'GET',
      '/bookings',
      null,
      { 'Authorization': `Bearer ${authToken}` }
    );
  }
  
  // Test 12: AI Chat Start (authenticated)
  if (authToken) {
    const chatData = {
      type: 'ONBOARDING',
      userRole: 'EXPERT',
      language: 'ar',
      context: {
        step: 'initial',
        userData: {
          firstName: 'أحمد',
          skills: ['React', 'Node.js']
        }
      }
    };
    
    await testEndpoint(
      'Start AI Chat',
      'POST',
      '/ai/chat/start',
      chatData,
      { 'Authorization': `Bearer ${authToken}` }
    );
  }
  
  // Test 13: Token Refresh
  if (loginResult?.data?.tokens?.refreshToken) {
    await testEndpoint(
      'Token Refresh',
      'POST',
      '/auth/refresh',
      { refreshToken: loginResult.data.tokens.refreshToken }
    );
  }
  
  // Test 14: Logout (authenticated)
  if (authToken) {
    await testEndpoint(
      'User Logout',
      'POST',
      '/auth/logout',
      null,
      { 'Authorization': `Bearer ${authToken}` }
    );
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('✅ Manual API Testing Complete!');
  console.log('\n📊 Test Summary:');
  console.log('   - Health check and API info endpoints');
  console.log('   - Authentication flow (register, login, profile, logout)');
  console.log('   - Services and experts listing');
  console.log('   - Search functionality');
  console.log('   - Service creation (authenticated)');
  console.log('   - Bookings management');
  console.log('   - AI chat integration');
  console.log('   - Token refresh mechanism');
  console.log('\n🎯 All core API endpoints tested successfully!');
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error.message);
  process.exit(1);
});

// Run the tests
runTests().catch((error) => {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
});
