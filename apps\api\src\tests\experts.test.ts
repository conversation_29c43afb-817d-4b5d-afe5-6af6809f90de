/**
 * Experts API Tests for Freela Syria
 * Tests all expert-related endpoints including profile management, search, and filtering
 */

import request from 'supertest';
import { app } from '../app';

describe('Experts API', () => {
  let testApp: any;
  let expertTokens: any;
  let clientTokens: any;
  let testExpertId: string;

  beforeAll(async () => {
    testApp = app;
    
    // Login as expert
    const expertLogin = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    expertTokens = expertLogin.body.data.tokens;

    // Login as client
    const clientLogin = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    clientTokens = clientLogin.body.data.tokens;
  });

  describe('GET /api/v1/experts', () => {
    it('should get all experts with pagination', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts')
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          experts: expect.any(Array),
          pagination: {
            page: 1,
            limit: 10,
            total: expect.any(Number),
            totalPages: expect.any(Number)
          }
        }
      });

      expect(response.body.data.experts.length).toBeGreaterThan(0);
      expect(response.body.data.experts[0]).toMatchObject({
        id: expect.any(String),
        firstName: expect.any(String),
        lastName: expect.any(String),
        title: expect.any(Object),
        bio: expect.any(Object),
        skills: expect.any(Array),
        rating: expect.any(Number),
        completedProjects: expect.any(Number)
      });
    });

    it('should filter experts by skills', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts')
        .query({ skills: 'React,JavaScript' })
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.experts.forEach((expert: any) => {
        const hasReactOrJS = expert.skills.some((skill: string) => 
          skill.toLowerCase().includes('react') || skill.toLowerCase().includes('javascript')
        );
        expect(hasReactOrJS).toBe(true);
      });
    });

    it('should filter experts by category', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts')
        .query({ category: 'tech-development' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.experts).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            categories: expect.arrayContaining(['tech-development'])
          })
        ])
      );
    });

    it('should filter experts by rating', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts')
        .query({ minRating: 4.0 })
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.experts.forEach((expert: any) => {
        expect(expert.rating).toBeGreaterThanOrEqual(4.0);
      });
    });

    it('should sort experts by rating', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts')
        .query({ sortBy: 'rating', sortOrder: 'desc' })
        .expect(200);

      expect(response.body.success).toBe(true);
      const experts = response.body.data.experts;
      for (let i = 1; i < experts.length; i++) {
        expect(experts[i-1].rating).toBeGreaterThanOrEqual(experts[i].rating);
      }
    });
  });

  describe('GET /api/v1/experts/:id', () => {
    it('should get a specific expert by ID', async () => {
      // First get an expert ID from the list
      const expertsResponse = await request(testApp)
        .get('/api/v1/experts')
        .query({ limit: 1 });
      
      const expertId = expertsResponse.body.data.experts[0].id;
      testExpertId = expertId;

      const response = await request(testApp)
        .get(`/api/v1/experts/${expertId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          expert: {
            id: expertId,
            firstName: expect.any(String),
            lastName: expect.any(String),
            title: expect.any(Object),
            bio: expect.any(Object),
            skills: expect.any(Array),
            categories: expect.any(Array),
            rating: expect.any(Number),
            completedProjects: expect.any(Number),
            languages: expect.any(Array),
            hourlyRate: expect.any(Number),
            availability: expect.any(String),
            responseTime: expect.any(String),
            services: expect.any(Array)
          }
        }
      });
    });

    it('should return 404 for non-existent expert', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts/non-existent-id')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Expert not found')
      });
    });
  });

  describe('PUT /api/v1/experts/profile', () => {
    it('should update expert profile successfully', async () => {
      const updateData = {
        title: {
          en: 'Senior Full-Stack Developer',
          ar: 'مطور ويب متقدم متكامل'
        },
        bio: {
          en: 'Experienced developer with 8+ years in web development',
          ar: 'مطور خبير مع أكثر من 8 سنوات في تطوير الويب'
        },
        skills: ['React', 'Node.js', 'TypeScript', 'Arabic Localization'],
        categories: ['tech-development', 'web-design'],
        hourlyRate: 45,
        availability: 'FULL_TIME',
        responseTime: 'WITHIN_1_HOUR',
        languages: ['Arabic', 'English'],
        location: {
          city: 'Damascus',
          country: 'Syria',
          timezone: 'Asia/Damascus'
        }
      };

      const response = await request(testApp)
        .put('/api/v1/experts/profile')
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Profile updated successfully'),
        data: {
          expert: {
            title: updateData.title,
            bio: updateData.bio,
            skills: updateData.skills,
            categories: updateData.categories,
            hourlyRate: updateData.hourlyRate,
            availability: updateData.availability,
            responseTime: updateData.responseTime,
            languages: updateData.languages
          }
        }
      });
    });

    it('should reject profile update without authentication', async () => {
      const updateData = {
        title: { en: 'Test Title', ar: 'عنوان تجريبي' }
      };

      const response = await request(testApp)
        .put('/api/v1/experts/profile')
        .send(updateData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });

    it('should reject profile update from non-expert user', async () => {
      const updateData = {
        title: { en: 'Test Title', ar: 'عنوان تجريبي' }
      };

      const response = await request(testApp)
        .put('/api/v1/experts/profile')
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .send(updateData)
        .expect(403);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Access denied')
      });
    });

    it('should validate required fields', async () => {
      const invalidData = {
        hourlyRate: -10, // Invalid negative rate
        skills: [], // Empty skills array
        categories: ['invalid-category'] // Invalid category
      };

      const response = await request(testApp)
        .put('/api/v1/experts/profile')
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Validation failed'
      });
    });
  });

  describe('GET /api/v1/experts/profile', () => {
    it('should get own expert profile', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts/profile')
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          expert: {
            id: expect.any(String),
            firstName: expect.any(String),
            lastName: expect.any(String),
            email: expect.any(String),
            title: expect.any(Object),
            bio: expect.any(Object),
            skills: expect.any(Array),
            categories: expect.any(Array),
            rating: expect.any(Number),
            completedProjects: expect.any(Number)
          }
        }
      });
    });

    it('should reject profile request without authentication', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts/profile')
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('GET /api/v1/experts/:id/services', () => {
    it('should get services by expert ID', async () => {
      if (!testExpertId) {
        const expertsResponse = await request(testApp)
          .get('/api/v1/experts')
          .query({ limit: 1 });
        testExpertId = expertsResponse.body.data.experts[0].id;
      }

      const response = await request(testApp)
        .get(`/api/v1/experts/${testExpertId}/services`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          services: expect.any(Array),
          expert: {
            id: testExpertId,
            firstName: expect.any(String),
            lastName: expect.any(String)
          }
        }
      });
    });

    it('should return empty array for expert with no services', async () => {
      // This test assumes there might be experts without services
      const response = await request(testApp)
        .get(`/api/v1/experts/${testExpertId}/services`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.services)).toBe(true);
    });
  });

  describe('GET /api/v1/experts/search', () => {
    it('should search experts by query', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts/search')
        .query({ q: 'developer' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          experts: expect.any(Array),
          total: expect.any(Number)
        }
      });
    });

    it('should search experts with location filter', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts/search')
        .query({ q: 'developer', location: 'Damascus' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.experts)).toBe(true);
    });

    it('should return empty results for non-matching query', async () => {
      const response = await request(testApp)
        .get('/api/v1/experts/search')
        .query({ q: 'nonexistentskillxyz123' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          experts: [],
          total: 0
        }
      });
    });
  });
});
