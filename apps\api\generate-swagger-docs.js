/**
 * Swagger Documentation Generator for Freela Syria API
 * Generates comprehensive API documentation with Arabic RTL support
 */

const fs = require('fs');
const path = require('path');

const swaggerSpec = {
  openapi: '3.0.0',
  info: {
    title: 'Freela Syria API',
    version: '1.0.0',
    description: `
# Freela Syria API Documentation

AI-Powered Freelance Marketplace API for Syrian Experts

## Features
- 🔐 JWT Authentication with refresh tokens
- 🌍 Arabic RTL and English language support
- 🤖 AI-powered onboarding and recommendations
- 🔍 Advanced search and filtering
- 💬 Real-time messaging system
- 📊 Comprehensive analytics
- 🛡️ Enterprise-grade security

## Syrian Market Focus
This API is specifically designed for the Syrian freelance market with:
- Cultural context awareness
- Local payment methods support
- Arabic language prioritization
- Syrian geographic data integration
    `,
    contact: {
      name: 'Freela Syria Team',
      email: '<EMAIL>',
      url: 'https://freela-syria.com'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: 'http://localhost:3001/api/v1',
      description: 'Development server'
    },
    {
      url: 'https://api.freela-syria.com/v1',
      description: 'Production server'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT token obtained from login endpoint'
      }
    },
    schemas: {
      User: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          email: { type: 'string', format: 'email' },
          firstName: { type: 'string', example: 'أحمد' },
          lastName: { type: 'string', example: 'محمد' },
          phone: { type: 'string', example: '+963-11-1234567' },
          role: { type: 'string', enum: ['CLIENT', 'EXPERT', 'ADMIN'] },
          language: { type: 'string', enum: ['ar', 'en'], default: 'ar' },
          isVerified: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      Service: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          title: {
            type: 'object',
            properties: {
              en: { type: 'string', example: 'Professional Web Development' },
              ar: { type: 'string', example: 'تطوير مواقع ويب احترافية' }
            }
          },
          description: {
            type: 'object',
            properties: {
              en: { type: 'string' },
              ar: { type: 'string' }
            }
          },
          category: { type: 'string', example: 'tech-development' },
          subcategory: { type: 'string', example: 'Web Development' },
          tags: { type: 'array', items: { type: 'string' } },
          priceType: { type: 'string', enum: ['FIXED', 'HOURLY'] },
          basePrice: { type: 'number', minimum: 0 },
          deliveryTime: { type: 'integer', minimum: 1 },
          revisions: { type: 'integer', minimum: 0 },
          serviceType: { type: 'string', enum: ['DIGITAL', 'PHYSICAL'] },
          isActive: { type: 'boolean' },
          expertId: { type: 'string', format: 'uuid' },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      Expert: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          firstName: { type: 'string' },
          lastName: { type: 'string' },
          title: {
            type: 'object',
            properties: {
              en: { type: 'string', example: 'Full-Stack Developer' },
              ar: { type: 'string', example: 'مطور ويب متكامل' }
            }
          },
          bio: {
            type: 'object',
            properties: {
              en: { type: 'string' },
              ar: { type: 'string' }
            }
          },
          skills: { type: 'array', items: { type: 'string' } },
          categories: { type: 'array', items: { type: 'string' } },
          rating: { type: 'number', minimum: 0, maximum: 5 },
          completedProjects: { type: 'integer', minimum: 0 },
          hourlyRate: { type: 'number', minimum: 0 },
          availability: { type: 'string', enum: ['FULL_TIME', 'PART_TIME', 'UNAVAILABLE'] },
          responseTime: { type: 'string', enum: ['WITHIN_1_HOUR', 'WITHIN_6_HOURS', 'WITHIN_24_HOURS'] },
          languages: { type: 'array', items: { type: 'string' } }
        }
      },
      Booking: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          serviceId: { type: 'string', format: 'uuid' },
          clientId: { type: 'string', format: 'uuid' },
          expertId: { type: 'string', format: 'uuid' },
          message: { type: 'string' },
          requirements: { type: 'array', items: { type: 'string' } },
          budget: { type: 'number', minimum: 0 },
          deadline: { type: 'string', format: 'date-time' },
          status: { 
            type: 'string', 
            enum: ['PENDING', 'ACCEPTED', 'REJECTED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'] 
          },
          contactPreference: { type: 'string', enum: ['CHAT', 'EMAIL', 'PHONE'] },
          urgency: { type: 'string', enum: ['LOW', 'MEDIUM', 'HIGH'] },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      AIConversation: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          type: { type: 'string', enum: ['ONBOARDING', 'SUPPORT', 'RECOMMENDATION'] },
          userRole: { type: 'string', enum: ['CLIENT', 'EXPERT'] },
          language: { type: 'string', enum: ['ar', 'en'] },
          status: { type: 'string', enum: ['ACTIVE', 'COMPLETED', 'PAUSED'] },
          userId: { type: 'string', format: 'uuid' },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      Error: {
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string' },
          code: { type: 'string' },
          details: { type: 'object' }
        }
      },
      SuccessResponse: {
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string' },
          data: { type: 'object' }
        }
      }
    },
    responses: {
      UnauthorizedError: {
        description: 'Authentication required',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      },
      ForbiddenError: {
        description: 'Access denied',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      },
      NotFoundError: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      },
      ValidationError: {
        description: 'Validation failed',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ],
  tags: [
    {
      name: 'Authentication',
      description: 'User authentication and authorization endpoints'
    },
    {
      name: 'Services',
      description: 'Service management endpoints'
    },
    {
      name: 'Experts',
      description: 'Expert profile management endpoints'
    },
    {
      name: 'Bookings',
      description: 'Booking management endpoints'
    },
    {
      name: 'Search',
      description: 'Search and filtering endpoints'
    },
    {
      name: 'AI Integration',
      description: 'AI-powered features and recommendations'
    }
  ],
  paths: {
    '/health': {
      get: {
        tags: ['System'],
        summary: 'Health check endpoint',
        description: 'Check API server health and service status',
        responses: {
          '200': {
            description: 'Health status',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', example: 'ok' },
                    timestamp: { type: 'string', format: 'date-time' },
                    uptime: { type: 'number' },
                    services: {
                      type: 'object',
                      properties: {
                        database: { type: 'object' },
                        redis: { type: 'object' }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
};

// Generate the documentation file
function generateSwaggerDocs() {
  const outputPath = path.join(__dirname, 'swagger-spec.json');
  
  try {
    fs.writeFileSync(outputPath, JSON.stringify(swaggerSpec, null, 2));
    console.log('✅ Swagger documentation generated successfully!');
    console.log(`📄 File saved to: ${outputPath}`);
    console.log(`🌐 View at: http://localhost:3001/api/v1/docs`);
    
    return swaggerSpec;
  } catch (error) {
    console.error('❌ Failed to generate Swagger documentation:', error.message);
    throw error;
  }
}

// Export for use in other modules
module.exports = { swaggerSpec, generateSwaggerDocs };

// Run if called directly
if (require.main === module) {
  generateSwaggerDocs();
}
