/**
 * Search API Tests for Freela Syria
 * Tests all search-related endpoints including global search, filters, and location-based search
 */

import request from 'supertest';
import { app } from '../app';

describe('Search API', () => {
  let testApp: any;
  let authTokens: any;

  beforeAll(async () => {
    testApp = app;
    
    // Login to get auth tokens for authenticated search features
    const loginResponse = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    authTokens = loginResponse.body.data.tokens;
  });

  describe('GET /api/v1/search', () => {
    it('should perform global search with query', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ q: 'web development' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          services: expect.any(Array),
          experts: expect.any(Array),
          total: expect.any(Number),
          query: 'web development'
        }
      });
    });

    it('should search with Arabic query', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ q: 'تطوير مواقع' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          services: expect.any(Array),
          experts: expect.any(Array),
          total: expect.any(Number),
          query: 'تطوير مواقع'
        }
      });
    });

    it('should filter search by type', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ q: 'developer', type: 'services' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          services: expect.any(Array),
          experts: [],
          total: expect.any(Number),
          type: 'services'
        }
      });
    });

    it('should filter search by category', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ q: 'development', category: 'tech-development' })
        .expect(200);

      expect(response.body.success).toBe(true);
      
      // Check that services match the category
      response.body.data.services.forEach((service: any) => {
        expect(service.category).toBe('tech-development');
      });
    });

    it('should filter search by price range', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ 
          q: 'development', 
          minPrice: 200, 
          maxPrice: 1000 
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      
      // Check that services are within price range
      response.body.data.services.forEach((service: any) => {
        expect(service.basePrice).toBeGreaterThanOrEqual(200);
        expect(service.basePrice).toBeLessThanOrEqual(1000);
      });
    });

    it('should search with location filter', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ 
          q: 'developer', 
          location: 'Damascus' 
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.services)).toBe(true);
      expect(Array.isArray(response.body.data.experts)).toBe(true);
    });

    it('should handle pagination', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ 
          q: 'development', 
          page: 1, 
          limit: 5 
        })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          services: expect.any(Array),
          experts: expect.any(Array),
          pagination: {
            page: 1,
            limit: 5,
            total: expect.any(Number),
            totalPages: expect.any(Number)
          }
        }
      });

      expect(response.body.data.services.length).toBeLessThanOrEqual(5);
    });

    it('should sort search results', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ 
          q: 'development', 
          sortBy: 'price', 
          sortOrder: 'asc' 
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      
      const services = response.body.data.services;
      if (services.length > 1) {
        for (let i = 1; i < services.length; i++) {
          expect(services[i-1].basePrice).toBeLessThanOrEqual(services[i].basePrice);
        }
      }
    });

    it('should return empty results for non-matching query', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .query({ q: 'nonexistentservicexyz123' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          services: [],
          experts: [],
          total: 0
        }
      });
    });

    it('should require query parameter', async () => {
      const response = await request(testApp)
        .get('/api/v1/search')
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Search query is required')
      });
    });
  });

  describe('GET /api/v1/search/suggestions', () => {
    it('should get search suggestions', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/suggestions')
        .query({ q: 'web' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          suggestions: expect.any(Array)
        }
      });

      expect(response.body.data.suggestions.length).toBeLessThanOrEqual(10);
    });

    it('should get Arabic search suggestions', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/suggestions')
        .query({ q: 'تطوير' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          suggestions: expect.any(Array)
        }
      });
    });

    it('should limit suggestions count', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/suggestions')
        .query({ q: 'development', limit: 5 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.suggestions.length).toBeLessThanOrEqual(5);
    });

    it('should return empty suggestions for short query', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/suggestions')
        .query({ q: 'a' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          suggestions: []
        }
      });
    });
  });

  describe('GET /api/v1/search/filters', () => {
    it('should get available search filters', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/filters')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          categories: expect.any(Array),
          serviceTypes: expect.any(Array),
          priceRanges: expect.any(Array),
          locations: expect.any(Array),
          skills: expect.any(Array)
        }
      });

      // Verify categories include expected Syrian market categories
      expect(response.body.data.categories).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'tech-development',
            name: expect.any(Object) // Should have en/ar translations
          })
        ])
      );
    });

    it('should get category-specific filters', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/filters')
        .query({ category: 'tech-development' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.subcategories).toBeDefined();
      expect(Array.isArray(response.body.data.subcategories)).toBe(true);
    });
  });

  describe('GET /api/v1/search/trending', () => {
    it('should get trending searches', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/trending')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          trending: expect.any(Array)
        }
      });

      expect(response.body.data.trending.length).toBeLessThanOrEqual(20);
    });

    it('should get trending searches by category', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/trending')
        .query({ category: 'tech-development' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.trending)).toBe(true);
    });

    it('should limit trending results', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/trending')
        .query({ limit: 5 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.trending.length).toBeLessThanOrEqual(5);
    });
  });

  describe('POST /api/v1/search/save', () => {
    it('should save search query for authenticated user', async () => {
      const searchData = {
        query: 'web development Damascus',
        filters: {
          category: 'tech-development',
          location: 'Damascus',
          priceRange: '500-1000'
        }
      };

      const response = await request(testApp)
        .post('/api/v1/search/save')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(searchData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Search saved successfully'),
        data: {
          savedSearch: {
            id: expect.any(String),
            query: searchData.query,
            filters: searchData.filters,
            userId: expect.any(String)
          }
        }
      });
    });

    it('should reject save search without authentication', async () => {
      const searchData = {
        query: 'test search',
        filters: {}
      };

      const response = await request(testApp)
        .post('/api/v1/search/save')
        .send(searchData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('GET /api/v1/search/saved', () => {
    it('should get saved searches for authenticated user', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/saved')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          savedSearches: expect.any(Array)
        }
      });
    });

    it('should reject saved searches request without authentication', async () => {
      const response = await request(testApp)
        .get('/api/v1/search/saved')
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });
});
