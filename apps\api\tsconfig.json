{"compilerOptions": {"outDir": "./dist", "target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node", "jest"], "noEmit": false, "baseUrl": ".", "paths": {"@freela/ui": ["../../packages/ui/src"], "@freela/utils": ["../../packages/utils/src"], "@freela/types": ["../../packages/types/src"], "@freela/database": ["../../packages/database/src"], "@freela/i18n": ["../../packages/i18n/src"], "@freela/config": ["../../packages/config/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}