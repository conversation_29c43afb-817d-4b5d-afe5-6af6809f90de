/**
 * Bookings API Tests for Freela Syria
 * Tests all booking-related endpoints including creation, management, and status updates
 */

import request from 'supertest';
import { app } from '../app';

describe('Bookings API', () => {
  let testApp: any;
  let expertTokens: any;
  let clientTokens: any;
  let testBookingId: string;
  let testServiceId: string;

  beforeAll(async () => {
    testApp = app;
    
    // Login as expert
    const expertLogin = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    expertTokens = expertLogin.body.data.tokens;

    // Login as client
    const clientLogin = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    clientTokens = clientLogin.body.data.tokens;

    // Get a service ID for booking tests
    const servicesResponse = await request(testApp)
      .get('/api/v1/services')
      .query({ limit: 1 });
    
    if (servicesResponse.body.data.services.length > 0) {
      testServiceId = servicesResponse.body.data.services[0].id;
    }
  });

  describe('POST /api/v1/bookings', () => {
    it('should create a new booking successfully', async () => {
      const bookingData = {
        serviceId: testServiceId,
        message: 'I need a professional website for my business. Please provide a detailed quote.',
        requirements: [
          'Responsive design for mobile and desktop',
          'Arabic RTL support',
          'Contact form integration',
          'SEO optimization'
        ],
        budget: 800,
        deadline: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString(), // 21 days from now
        contactPreference: 'CHAT',
        urgency: 'MEDIUM',
        additionalInfo: {
          projectType: 'Business Website',
          industry: 'Technology',
          targetAudience: 'Syrian market'
        }
      };

      const response = await request(testApp)
        .post('/api/v1/bookings')
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .send(bookingData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Booking created successfully'),
        data: {
          booking: {
            id: expect.any(String),
            serviceId: bookingData.serviceId,
            message: bookingData.message,
            requirements: bookingData.requirements,
            budget: bookingData.budget,
            contactPreference: bookingData.contactPreference,
            urgency: bookingData.urgency,
            status: 'PENDING',
            clientId: expect.any(String),
            expertId: expect.any(String)
          }
        }
      });

      testBookingId = response.body.data.booking.id;
    });

    it('should reject booking creation without authentication', async () => {
      const bookingData = {
        serviceId: testServiceId,
        message: 'Test booking message',
        budget: 500
      };

      const response = await request(testApp)
        .post('/api/v1/bookings')
        .send(bookingData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });

    it('should reject booking with invalid service ID', async () => {
      const bookingData = {
        serviceId: 'non-existent-service-id',
        message: 'Test booking message',
        budget: 500
      };

      const response = await request(testApp)
        .post('/api/v1/bookings')
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .send(bookingData)
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Service not found')
      });
    });

    it('should validate required fields', async () => {
      const invalidBookingData = {
        serviceId: testServiceId,
        message: '', // Empty message
        budget: -100 // Negative budget
      };

      const response = await request(testApp)
        .post('/api/v1/bookings')
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .send(invalidBookingData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Validation failed'
      });
    });
  });

  describe('GET /api/v1/bookings', () => {
    it('should get user bookings with pagination', async () => {
      const response = await request(testApp)
        .get('/api/v1/bookings')
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          bookings: expect.any(Array),
          pagination: {
            page: 1,
            limit: 10,
            total: expect.any(Number),
            totalPages: expect.any(Number)
          }
        }
      });
    });

    it('should filter bookings by status', async () => {
      const response = await request(testApp)
        .get('/api/v1/bookings')
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .query({ status: 'PENDING' })
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.bookings.forEach((booking: any) => {
        expect(booking.status).toBe('PENDING');
      });
    });

    it('should get expert bookings', async () => {
      const response = await request(testApp)
        .get('/api/v1/bookings')
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .query({ role: 'expert' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.bookings)).toBe(true);
    });

    it('should reject bookings request without authentication', async () => {
      const response = await request(testApp)
        .get('/api/v1/bookings')
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('GET /api/v1/bookings/:id', () => {
    it('should get a specific booking by ID', async () => {
      if (!testBookingId) {
        // Create a booking first
        const bookingResponse = await request(testApp)
          .post('/api/v1/bookings')
          .set('Authorization', `Bearer ${clientTokens.accessToken}`)
          .send({
            serviceId: testServiceId,
            message: 'Test booking for ID test',
            budget: 600
          });
        testBookingId = bookingResponse.body.data.booking.id;
      }

      const response = await request(testApp)
        .get(`/api/v1/bookings/${testBookingId}`)
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          booking: {
            id: testBookingId,
            serviceId: expect.any(String),
            message: expect.any(String),
            budget: expect.any(Number),
            status: expect.any(String),
            clientId: expect.any(String),
            expertId: expect.any(String),
            service: expect.any(Object),
            client: expect.any(Object),
            expert: expect.any(Object)
          }
        }
      });
    });

    it('should return 404 for non-existent booking', async () => {
      const response = await request(testApp)
        .get('/api/v1/bookings/non-existent-id')
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Booking not found')
      });
    });

    it('should reject access to other user\'s booking', async () => {
      // This test would need a booking from another user
      // For now, we'll test with a different user token
      const response = await request(testApp)
        .get(`/api/v1/bookings/${testBookingId}`)
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .expect(200); // Expert should be able to see their bookings

      expect(response.body.success).toBe(true);
    });
  });

  describe('PUT /api/v1/bookings/:id/status', () => {
    it('should update booking status as expert', async () => {
      if (!testBookingId) {
        // Create a booking first
        const bookingResponse = await request(testApp)
          .post('/api/v1/bookings')
          .set('Authorization', `Bearer ${clientTokens.accessToken}`)
          .send({
            serviceId: testServiceId,
            message: 'Test booking for status update',
            budget: 700
          });
        testBookingId = bookingResponse.body.data.booking.id;
      }

      const statusUpdate = {
        status: 'ACCEPTED',
        message: 'I accept this project. Let\'s discuss the details.',
        estimatedDelivery: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
      };

      const response = await request(testApp)
        .put(`/api/v1/bookings/${testBookingId}/status`)
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(statusUpdate)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Booking status updated'),
        data: {
          booking: {
            id: testBookingId,
            status: statusUpdate.status
          }
        }
      });
    });

    it('should reject status update without authentication', async () => {
      const response = await request(testApp)
        .put(`/api/v1/bookings/${testBookingId}/status`)
        .send({ status: 'ACCEPTED' })
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });

    it('should validate status values', async () => {
      const invalidStatusUpdate = {
        status: 'INVALID_STATUS'
      };

      const response = await request(testApp)
        .put(`/api/v1/bookings/${testBookingId}/status`)
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(invalidStatusUpdate)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Validation failed'
      });
    });
  });

  describe('POST /api/v1/bookings/:id/messages', () => {
    it('should add message to booking', async () => {
      if (!testBookingId) {
        // Create a booking first
        const bookingResponse = await request(testApp)
          .post('/api/v1/bookings')
          .set('Authorization', `Bearer ${clientTokens.accessToken}`)
          .send({
            serviceId: testServiceId,
            message: 'Test booking for messaging',
            budget: 650
          });
        testBookingId = bookingResponse.body.data.booking.id;
      }

      const messageData = {
        message: 'Thank you for your interest. When can we start the project?',
        type: 'TEXT'
      };

      const response = await request(testApp)
        .post(`/api/v1/bookings/${testBookingId}/messages`)
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(messageData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Message sent successfully'),
        data: {
          message: {
            id: expect.any(String),
            bookingId: testBookingId,
            message: messageData.message,
            type: messageData.type,
            senderId: expect.any(String),
            createdAt: expect.any(String)
          }
        }
      });
    });

    it('should reject message without authentication', async () => {
      const response = await request(testApp)
        .post(`/api/v1/bookings/${testBookingId}/messages`)
        .send({ message: 'Test message' })
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('GET /api/v1/bookings/:id/messages', () => {
    it('should get booking messages', async () => {
      const response = await request(testApp)
        .get(`/api/v1/bookings/${testBookingId}/messages`)
        .set('Authorization', `Bearer ${clientTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          messages: expect.any(Array)
        }
      });
    });

    it('should reject messages request without authentication', async () => {
      const response = await request(testApp)
        .get(`/api/v1/bookings/${testBookingId}/messages`)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });
});
