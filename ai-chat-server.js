const express = require('express');
const cors = require('cors');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = 3006; // Different port to avoid conflicts

// Middleware
app.use(cors({
  origin: ['http://localhost:3004', 'http://localhost:19006', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

// OpenRouter Configuration
const OPENROUTER_API_KEY = '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';
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1';

// In-memory session storage (for testing only)
const sessions = new Map();

// Syrian cultural context prompts
const EXPERT_SYSTEM_PROMPT = `
أنت مساعد ذكي متخصص في مساعدة الخبراء السوريين على منصة فريلا سوريا.

مهمتك:
1. مساعدة الخبراء في إعداد ملفاتهم الشخصية
2. تحديد مهاراتهم وخبراتهم
3. اقتراح خدمات مناسبة للسوق السوري
4. تقديم نصائح لتحسين فرص الحصول على مشاريع

السياق الثقافي السوري:
- احترم التقاليد والقيم السورية
- استخدم اللغة العربية الفصحى مع بعض العامية المفهومة
- اعتبر الظروف الاقتصادية الحالية في سوريا
- ركز على المهارات التقنية والحرفية المطلوبة محلياً

كن ودوداً ومشجعاً ومفيداً في جميع تفاعلاتك.
`;

const CLIENT_SYSTEM_PROMPT = `
أنت مساعد ذكي متخصص في مساعدة العملاء السوريين على منصة فريلا سوريا.

مهمتك:
1. فهم احتياجات العملاء ومتطلبات مشاريعهم
2. مساعدتهم في تحديد نوع الخدمات المطلوبة
3. تقديم نصائح لكتابة وصف مشروع واضح
4. اقتراح الميزانية المناسبة للسوق السوري

السياق الثقافي السوري:
- احترم التقاليد والقيم السورية
- استخدم اللغة العربية الفصحى مع بعض العامية المفهومة
- اعتبر الظروف الاقتصادية الحالية في سوريا
- ركز على الحلول العملية والمناسبة للسوق المحلي

كن ودوداً ومساعداً ومفهماً لاحتياجات العملاء.
`;

// Helper function to call OpenRouter API
async function callOpenRouter(messages, options = {}) {
  try {
    const response = await axios.post(`${OPENROUTER_API_URL}/chat/completions`, {
      model: options.model || 'openai/gpt-4-turbo-preview',
      messages: messages,
      max_tokens: options.maxTokens || 800,
      temperature: options.temperature || 0.7,
      stream: false
    }, {
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://freela-syria.com',
        'X-Title': 'Freela Syria AI Chat Server'
      }
    });

    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('OpenRouter API Error:', error.response?.data || error.message);
    throw new Error('Failed to get AI response');
  }
}

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'AI Chat Server',
    port: PORT
  });
});

// Start AI conversation
app.post('/api/v1/ai/chat/start', async (req, res) => {
  try {
    const { userRole, language = 'ar', sessionType = 'onboarding', metadata = {} } = req.body;

    if (!userRole || !['CLIENT', 'EXPERT'].includes(userRole)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid userRole. Must be CLIENT or EXPERT'
      });
    }

    // Create new session
    const sessionId = uuidv4();
    const systemPrompt = userRole === 'EXPERT' ? EXPERT_SYSTEM_PROMPT : CLIENT_SYSTEM_PROMPT;

    // Initial greeting message
    const initialMessages = [
      { role: 'system', content: systemPrompt },
      { 
        role: 'user', 
        content: userRole === 'EXPERT' 
          ? 'مرحبا، أريد إنشاء ملف شخصي كخبير على منصة فريلا سوريا'
          : 'مرحبا، أريد العثور على خبراء لمشروعي على منصة فريلا سوريا'
      }
    ];

    const initialResponse = await callOpenRouter(initialMessages);

    // Store session
    const session = {
      id: sessionId,
      userRole,
      language,
      sessionType,
      metadata,
      messages: [
        { role: 'system', content: systemPrompt },
        { 
          role: 'user', 
          content: userRole === 'EXPERT' 
            ? 'مرحبا، أريد إنشاء ملف شخصي كخبير على منصة فريلا سوريا'
            : 'مرحبا، أريد العثور على خبراء لمشروعي على منصة فريلا سوريا'
        },
        { role: 'assistant', content: initialResponse }
      ],
      createdAt: new Date(),
      lastActiveAt: new Date(),
      currentStep: 'introduction',
      extractedData: {}
    };

    sessions.set(sessionId, session);

    res.status(201).json({
      success: true,
      data: {
        sessionId,
        initialMessage: {
          id: uuidv4(),
          sessionId,
          role: 'assistant',
          content: initialResponse,
          messageType: 'text',
          timestamp: new Date()
        },
        userRole,
        language,
        sessionType
      }
    });

  } catch (error) {
    console.error('Start conversation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start AI conversation',
      error: error.message
    });
  }
});

// Send message to AI
app.post('/api/v1/ai/chat/message', async (req, res) => {
  try {
    const { sessionId, message, messageType = 'text' } = req.body;

    if (!sessionId || !message) {
      return res.status(400).json({
        success: false,
        message: 'sessionId and message are required'
      });
    }

    const session = sessions.get(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Session not found'
      });
    }

    // Add user message to session
    const userMessage = { role: 'user', content: message };
    session.messages.push(userMessage);

    // Get AI response
    const aiResponse = await callOpenRouter(session.messages);

    // Add AI response to session
    const aiMessage = { role: 'assistant', content: aiResponse };
    session.messages.push(aiMessage);

    // Update session
    session.lastActiveAt = new Date();
    sessions.set(sessionId, session);

    res.json({
      success: true,
      data: {
        aiMessage: {
          id: uuidv4(),
          sessionId,
          role: 'assistant',
          content: aiResponse,
          messageType: 'text',
          timestamp: new Date()
        },
        isCompleted: false,
        currentStep: session.currentStep
      }
    });

  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process message',
      error: error.message
    });
  }
});

// Get session status
app.get('/api/v1/ai/chat/session/:sessionId', (req, res) => {
  try {
    const { sessionId } = req.params;
    const session = sessions.get(sessionId);

    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Session not found'
      });
    }

    res.json({
      success: true,
      data: {
        sessionId: session.id,
        userRole: session.userRole,
        language: session.language,
        sessionType: session.sessionType,
        currentStep: session.currentStep,
        messages: session.messages.filter(m => m.role !== 'system'),
        createdAt: session.createdAt,
        lastActiveAt: session.lastActiveAt,
        extractedData: session.extractedData
      }
    });

  } catch (error) {
    console.error('Get session error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get session',
      error: error.message
    });
  }
});

// Test OpenRouter connection
app.get('/api/v1/ai/test', async (req, res) => {
  try {
    const testResponse = await callOpenRouter([
      { role: 'user', content: 'مرحبا، هل تعمل بشكل صحيح؟' }
    ]);

    res.json({
      success: true,
      message: 'OpenRouter API is working correctly',
      response: testResponse
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'OpenRouter API test failed',
      error: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🤖 AI Chat Server running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/v1/ai/test`);
  console.log(`💬 Chat endpoints:`);
  console.log(`   POST /api/v1/ai/chat/start`);
  console.log(`   POST /api/v1/ai/chat/message`);
  console.log(`   GET  /api/v1/ai/chat/session/:sessionId`);
});
