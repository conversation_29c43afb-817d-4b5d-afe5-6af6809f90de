/**
 * Jest Test Setup for Freela Syria API
 * Configures test environment, database connections, and global test utilities
 */

import { config } from '../config';
import { logger } from '../utils/logger';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// Note: External service mocks are handled in individual test files as needed

// Extend global namespace for test utilities
declare global {
  var testUtils: {
    testUsers: any;
    testService: any;
    testBooking: any;
    generateRandomEmail: () => string;
    generateRandomPhone: () => string;
    wait: (ms: number) => Promise<void>;
  };
}

// Global test utilities
(global as any).testUtils = {
  // Test user data
  testUsers: {
    expert: {
      email: '<EMAIL>',
      password: 'TestPass123!',
      firstName: 'أحمد',
      lastName: 'محمد',
      phone: '+963-11-1234567',
      role: 'EXPERT',
      language: 'ar'
    },
    client: {
      email: '<EMAIL>',
      password: 'TestPass123!',
      firstName: 'فاطمة',
      lastName: 'أحمد',
      phone: '+963-21-2345678',
      role: 'CLIENT',
      language: 'ar'
    },
    admin: {
      email: '<EMAIL>',
      password: 'AdminPass123!',
      firstName: 'Admin',
      lastName: 'User',
      phone: '+963-11-9999999',
      role: 'ADMIN',
      language: 'en'
    }
  },

  // Test service data
  testService: {
    title: {
      en: 'Test Web Development Service',
      ar: 'خدمة تطوير مواقع الويب التجريبية'
    },
    description: {
      en: 'Professional web development service for testing',
      ar: 'خدمة تطوير مواقع ويب احترافية للاختبار'
    },
    category: 'tech-development',
    subcategory: 'Web Development',
    tags: ['React', 'Node.js', 'JavaScript'],
    priceType: 'FIXED',
    basePrice: 500,
    deliveryTime: 7,
    revisions: 3,
    serviceType: 'DIGITAL',
    isActive: true
  },

  // Test booking data
  testBooking: {
    message: 'I need a professional website for my business. Please provide a quote.',
    requirements: ['Responsive design', 'Arabic RTL support', 'Contact form'],
    budget: 600,
    deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
    contactPreference: 'CHAT',
    urgency: 'MEDIUM'
  },

  // Helper functions
  generateRandomEmail: () => `test.${Date.now()}@freela.sy`,
  generateRandomPhone: () => `+963-11-${Math.floor(Math.random() * 9000000) + 1000000}`,
  
  // Wait helper for async operations
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
};

// Setup and teardown hooks are handled in individual test files

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

export {};
