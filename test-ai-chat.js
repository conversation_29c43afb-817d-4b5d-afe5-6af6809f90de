const axios = require('axios');

const API_BASE_URL = 'http://localhost:3005/api/v1';
const API_BASE_URL_ISOLATED = 'http://localhost:3006/api/v1';

async function testAIChat() {
  console.log('🧪 Testing AI Chat Functionality (Isolated Server)...\n');

  try {
    // Test 1: Start AI conversation
    console.log('1️⃣ Testing AI conversation start...');
    const startResponse = await axios.post(`${API_BASE_URL_ISOLATED}/ai/chat/start`, {
      userRole: 'EXPERT',
      language: 'ar',
      sessionType: 'onboarding',
      metadata: {
        location: '',
        dialect: 'general'
      }
    });

    console.log('✅ AI conversation started successfully');
    console.log('Session ID:', startResponse.data.data.sessionId);
    console.log('Initial message:', startResponse.data.data.initialMessage);

    const sessionId = startResponse.data.data.sessionId;

    // Test 2: Send a message to AI
    console.log('\n2️⃣ Testing AI message sending...');
    const messageResponse = await axios.post(`${API_BASE_URL_ISOLATED}/ai/chat/message`, {
      sessionId: sessionId,
      message: 'مرحبا، أنا مطور ويب وأريد إنشاء ملف شخصي على المنصة',
      messageType: 'text'
    });

    console.log('✅ AI message sent successfully');
    console.log('AI Response:', messageResponse.data.data.aiMessage.content);

    // Test 3: Get session status
    console.log('\n3️⃣ Testing session status...');
    const statusResponse = await axios.get(`${API_BASE_URL_ISOLATED}/ai/chat/session/${sessionId}`);

    console.log('✅ Session status retrieved successfully');
    console.log('Current step:', statusResponse.data.data.currentStep);
    console.log('Message count:', statusResponse.data.data.messages.length);

    console.log('\n🎉 All AI chat tests passed successfully!');

  } catch (error) {
    console.error('❌ AI chat test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test OpenRouter API directly
async function testOpenRouterDirect() {
  console.log('\n🔧 Testing OpenRouter API directly...\n');

  try {
    const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
      model: 'openai/gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: 'أنت مساعد ذكي لمنصة فريلا سوريا. تساعد الخبراء السوريين في إعداد ملفاتهم الشخصية.'
        },
        {
          role: 'user',
          content: 'مرحبا، أنا مطور ويب وأريد إنشاء ملف شخصي على المنصة'
        }
      ],
      max_tokens: 200,
      temperature: 0.8
    }, {
      headers: {
        'Authorization': 'Bearer 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',
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://freela-syria.com',
        'X-Title': 'Freela Syria AI Test'
      }
    });

    console.log('✅ OpenRouter API working correctly');
    console.log('Response:', response.data.choices[0].message.content);

  } catch (error) {
    console.error('❌ OpenRouter API test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test the main API with authentication
async function testMainAPIWithAuth() {
  console.log('\n🔐 Testing Main API with Authentication...\n');

  try {
    // Test 1: Test the v2 conversation start endpoint (requires auth)
    console.log('1️⃣ Testing v2 conversation start (with mock auth)...');

    // Create a mock JWT token for testing
    const mockToken = 'Bearer test-token-123';

    const startResponse = await axios.post(`${API_BASE_URL}/ai/v2/conversation/start`, {
      userRole: 'EXPERT',
      language: 'ar',
      sessionType: 'onboarding',
      culturalContext: {
        location: 'Damascus',
        dialect: 'general'
      }
    }, {
      headers: {
        'Authorization': mockToken,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Main API v2 conversation started successfully');
    console.log('Session ID:', startResponse.data.data.sessionId);

  } catch (error) {
    console.log('❌ Main API test failed (expected - needs real auth):');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.message || error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

async function runTests() {
  await testOpenRouterDirect();
  await testMainAPIWithAuth();
  await testAIChat();
}

runTests();
