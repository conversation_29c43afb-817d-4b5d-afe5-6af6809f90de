/**
 * Services API Tests for Freela Syria
 * Tests all service-related endpoints including CRUD operations, search, and filtering
 */

import request from 'supertest';
import { app } from '../app';

describe('Services API', () => {
  let testApp: any;
  let authTokens: any;
  let expertTokens: any;
  let testServiceId: string;

  beforeAll(async () => {
    testApp = app;
    
    // Login as expert to get auth tokens
    const expertLogin = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    expertTokens = expertLogin.body.data.tokens;

    // Login as client for some tests
    const clientLogin = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    authTokens = clientLogin.body.data.tokens;
  });

  describe('POST /api/v1/services', () => {
    it('should create a new service successfully', async () => {
      const serviceData = {
        title: {
          en: 'Professional Web Development',
          ar: 'تطوير مواقع ويب احترافية'
        },
        description: {
          en: 'I will create a professional website using modern technologies',
          ar: 'سأقوم بإنشاء موقع ويب احترافي باستخدام التقنيات الحديثة'
        },
        category: 'tech-development',
        subcategory: 'Web Development',
        tags: ['React', 'Node.js', 'JavaScript', 'Arabic'],
        priceType: 'FIXED',
        basePrice: 750,
        deliveryTime: 10,
        revisions: 2,
        serviceType: 'DIGITAL',
        isActive: true,
        requirements: [
          'Project requirements document',
          'Brand guidelines (if available)',
          'Content and images'
        ],
        features: [
          'Responsive design',
          'Arabic RTL support',
          'SEO optimization',
          'Contact forms'
        ]
      };

      const response = await request(testApp)
        .post('/api/v1/services')
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(serviceData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Service created successfully'),
        data: {
          service: {
            title: serviceData.title,
            description: serviceData.description,
            category: serviceData.category,
            subcategory: serviceData.subcategory,
            priceType: serviceData.priceType,
            basePrice: serviceData.basePrice,
            deliveryTime: serviceData.deliveryTime,
            serviceType: serviceData.serviceType,
            isActive: serviceData.isActive,
            expertId: expect.any(String),
            id: expect.any(String)
          }
        }
      });

      testServiceId = response.body.data.service.id;
    });

    it('should reject service creation without authentication', async () => {
      const serviceData = global.testUtils.testService;

      const response = await request(testApp)
        .post('/api/v1/services')
        .send(serviceData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });

    it('should reject service creation with invalid data', async () => {
      const invalidServiceData = {
        title: { en: '' }, // Missing Arabic title and empty English
        description: { en: 'Short' }, // Too short description
        category: 'invalid-category',
        basePrice: -100, // Negative price
        deliveryTime: 0 // Invalid delivery time
      };

      const response = await request(testApp)
        .post('/api/v1/services')
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(invalidServiceData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Validation failed'
      });
    });
  });

  describe('GET /api/v1/services', () => {
    it('should get all services with pagination', async () => {
      const response = await request(testApp)
        .get('/api/v1/services')
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          services: expect.any(Array),
          pagination: {
            page: 1,
            limit: 10,
            total: expect.any(Number),
            totalPages: expect.any(Number)
          }
        }
      });

      expect(response.body.data.services.length).toBeGreaterThan(0);
      expect(response.body.data.services[0]).toMatchObject({
        id: expect.any(String),
        title: expect.any(Object),
        description: expect.any(Object),
        category: expect.any(String),
        basePrice: expect.any(Number),
        expertId: expect.any(String)
      });
    });

    it('should filter services by category', async () => {
      const response = await request(testApp)
        .get('/api/v1/services')
        .query({ category: 'tech-development' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.services).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            category: 'tech-development'
          })
        ])
      );
    });

    it('should filter services by price range', async () => {
      const response = await request(testApp)
        .get('/api/v1/services')
        .query({ minPrice: 100, maxPrice: 1000 })
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.services.forEach((service: any) => {
        expect(service.basePrice).toBeGreaterThanOrEqual(100);
        expect(service.basePrice).toBeLessThanOrEqual(1000);
      });
    });

    it('should filter services by service type', async () => {
      const response = await request(testApp)
        .get('/api/v1/services')
        .query({ serviceType: 'DIGITAL' })
        .expect(200);

      expect(response.body.success).toBe(true);
      response.body.data.services.forEach((service: any) => {
        expect(service.serviceType).toBe('DIGITAL');
      });
    });
  });

  describe('GET /api/v1/services/:id', () => {
    it('should get a specific service by ID', async () => {
      // First get a service ID from the list
      const servicesResponse = await request(testApp)
        .get('/api/v1/services')
        .query({ limit: 1 });
      
      const serviceId = servicesResponse.body.data.services[0].id;

      const response = await request(testApp)
        .get(`/api/v1/services/${serviceId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          service: {
            id: serviceId,
            title: expect.any(Object),
            description: expect.any(Object),
            category: expect.any(String),
            basePrice: expect.any(Number),
            expertId: expect.any(String),
            expert: expect.any(Object)
          }
        }
      });
    });

    it('should return 404 for non-existent service', async () => {
      const response = await request(testApp)
        .get('/api/v1/services/non-existent-id')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Service not found')
      });
    });
  });

  describe('PUT /api/v1/services/:id', () => {
    it('should update own service successfully', async () => {
      if (!testServiceId) {
        // Create a service first
        const createResponse = await request(testApp)
          .post('/api/v1/services')
          .set('Authorization', `Bearer ${expertTokens.accessToken}`)
          .send(global.testUtils.testService);
        testServiceId = createResponse.body.data.service.id;
      }

      const updateData = {
        title: {
          en: 'Updated Web Development Service',
          ar: 'خدمة تطوير مواقع ويب محدثة'
        },
        basePrice: 900,
        deliveryTime: 14
      };

      const response = await request(testApp)
        .put(`/api/v1/services/${testServiceId}`)
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Service updated successfully'),
        data: {
          service: {
            id: testServiceId,
            title: updateData.title,
            basePrice: updateData.basePrice,
            deliveryTime: updateData.deliveryTime
          }
        }
      });
    });

    it('should reject update without authentication', async () => {
      const response = await request(testApp)
        .put(`/api/v1/services/${testServiceId}`)
        .send({ basePrice: 1000 })
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('DELETE /api/v1/services/:id', () => {
    it('should delete own service successfully', async () => {
      if (!testServiceId) {
        // Create a service first
        const createResponse = await request(testApp)
          .post('/api/v1/services')
          .set('Authorization', `Bearer ${expertTokens.accessToken}`)
          .send(global.testUtils.testService);
        testServiceId = createResponse.body.data.service.id;
      }

      const response = await request(testApp)
        .delete(`/api/v1/services/${testServiceId}`)
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Service deleted successfully')
      });
    });

    it('should reject delete without authentication', async () => {
      const response = await request(testApp)
        .delete('/api/v1/services/some-id')
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });
});
