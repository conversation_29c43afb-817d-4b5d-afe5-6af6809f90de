/**
 * AI Integration API Tests for Freela Syria
 * Tests all AI-related endpoints including onboarding, chat, and recommendations
 */

import request from 'supertest';
import { app } from '../app';

describe('AI Integration API', () => {
  let testApp: any;
  let authTokens: any;
  let expertTokens: any;
  let conversationId: string;

  beforeAll(async () => {
    testApp = app;
    
    // Login as client
    const clientLogin = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    authTokens = clientLogin.body.data.tokens;

    // Login as expert
    const expertLogin = await request(testApp)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    expertTokens = expertLogin.body.data.tokens;
  });

  describe('POST /api/v1/ai/chat/start', () => {
    it('should start AI chat conversation', async () => {
      const chatData = {
        type: 'ONBOARDING',
        userRole: 'CLIENT',
        language: 'ar',
        context: {
          step: 'initial',
          userData: {
            firstName: 'فاطمة',
            interests: ['web design', 'mobile apps']
          }
        }
      };

      const response = await request(testApp)
        .post('/api/v1/ai/chat/start')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(chatData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Chat conversation started'),
        data: {
          conversation: {
            id: expect.any(String),
            type: chatData.type,
            userRole: chatData.userRole,
            language: chatData.language,
            status: 'ACTIVE',
            userId: expect.any(String)
          },
          initialMessage: {
            id: expect.any(String),
            content: expect.any(String),
            role: 'ASSISTANT',
            language: 'ar'
          }
        }
      });

      conversationId = response.body.data.conversation.id;
    });

    it('should start expert onboarding conversation', async () => {
      const chatData = {
        type: 'ONBOARDING',
        userRole: 'EXPERT',
        language: 'en',
        context: {
          step: 'skills_assessment',
          userData: {
            firstName: 'Ahmad',
            skills: ['React', 'Node.js', 'TypeScript']
          }
        }
      };

      const response = await request(testApp)
        .post('/api/v1/ai/chat/start')
        .set('Authorization', `Bearer ${expertTokens.accessToken}`)
        .send(chatData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.conversation.userRole).toBe('EXPERT');
      expect(response.body.data.initialMessage.content).toContain('skills');
    });

    it('should reject chat start without authentication', async () => {
      const chatData = {
        type: 'ONBOARDING',
        userRole: 'CLIENT',
        language: 'ar'
      };

      const response = await request(testApp)
        .post('/api/v1/ai/chat/start')
        .send(chatData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });

    it('should validate required fields', async () => {
      const invalidChatData = {
        type: 'INVALID_TYPE',
        userRole: 'INVALID_ROLE'
      };

      const response = await request(testApp)
        .post('/api/v1/ai/chat/start')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(invalidChatData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Validation failed'
      });
    });
  });

  describe('POST /api/v1/ai/chat/:conversationId/message', () => {
    it('should send message to AI conversation', async () => {
      if (!conversationId) {
        // Start a conversation first
        const startResponse = await request(testApp)
          .post('/api/v1/ai/chat/start')
          .set('Authorization', `Bearer ${authTokens.accessToken}`)
          .send({
            type: 'ONBOARDING',
            userRole: 'CLIENT',
            language: 'ar'
          });
        conversationId = startResponse.body.data.conversation.id;
      }

      const messageData = {
        content: 'أريد إنشاء موقع ويب لشركتي الصغيرة',
        language: 'ar',
        context: {
          step: 'project_requirements',
          businessType: 'small_business'
        }
      };

      const response = await request(testApp)
        .post(`/api/v1/ai/chat/${conversationId}/message`)
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(messageData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Message sent successfully'),
        data: {
          userMessage: {
            id: expect.any(String),
            content: messageData.content,
            role: 'USER',
            language: 'ar'
          },
          aiResponse: {
            id: expect.any(String),
            content: expect.any(String),
            role: 'ASSISTANT',
            language: 'ar'
          }
        }
      });
    });

    it('should handle English messages', async () => {
      const messageData = {
        content: 'I need help with my expert profile setup',
        language: 'en',
        context: {
          step: 'profile_setup'
        }
      };

      const response = await request(testApp)
        .post(`/api/v1/ai/chat/${conversationId}/message`)
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(messageData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.aiResponse.language).toBe('en');
    });

    it('should reject message to non-existent conversation', async () => {
      const messageData = {
        content: 'Test message',
        language: 'en'
      };

      const response = await request(testApp)
        .post('/api/v1/ai/chat/non-existent-id/message')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(messageData)
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Conversation not found')
      });
    });

    it('should reject message without authentication', async () => {
      const messageData = {
        content: 'Test message',
        language: 'en'
      };

      const response = await request(testApp)
        .post(`/api/v1/ai/chat/${conversationId}/message`)
        .send(messageData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('GET /api/v1/ai/chat/:conversationId/history', () => {
    it('should get conversation history', async () => {
      const response = await request(testApp)
        .get(`/api/v1/ai/chat/${conversationId}/history`)
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          conversation: {
            id: conversationId,
            type: expect.any(String),
            status: expect.any(String)
          },
          messages: expect.any(Array)
        }
      });

      expect(response.body.data.messages.length).toBeGreaterThan(0);
      expect(response.body.data.messages[0]).toMatchObject({
        id: expect.any(String),
        content: expect.any(String),
        role: expect.any(String),
        createdAt: expect.any(String)
      });
    });

    it('should reject history request without authentication', async () => {
      const response = await request(testApp)
        .get(`/api/v1/ai/chat/${conversationId}/history`)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('POST /api/v1/ai/recommendations/services', () => {
    it('should get AI service recommendations', async () => {
      const requestData = {
        userPreferences: {
          budget: 800,
          category: 'tech-development',
          projectType: 'website',
          urgency: 'medium',
          language: 'ar'
        },
        projectDescription: 'أحتاج موقع ويب احترافي لشركتي مع دعم اللغة العربية',
        requirements: [
          'Responsive design',
          'Arabic RTL support',
          'Contact forms',
          'SEO optimization'
        ]
      };

      const response = await request(testApp)
        .post('/api/v1/ai/recommendations/services')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(requestData)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          recommendations: expect.any(Array),
          reasoning: expect.any(String),
          matchScore: expect.any(Number)
        }
      });

      expect(response.body.data.recommendations.length).toBeGreaterThan(0);
      expect(response.body.data.recommendations[0]).toMatchObject({
        service: expect.any(Object),
        matchScore: expect.any(Number),
        reasons: expect.any(Array)
      });
    });

    it('should handle expert skill-based recommendations', async () => {
      const requestData = {
        userPreferences: {
          budget: 1200,
          category: 'design-creative',
          projectType: 'branding',
          language: 'en'
        },
        projectDescription: 'I need a complete brand identity for my startup',
        requirements: [
          'Logo design',
          'Brand guidelines',
          'Business cards',
          'Website mockups'
        ]
      };

      const response = await request(testApp)
        .post('/api/v1/ai/recommendations/services')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(requestData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.recommendations)).toBe(true);
    });

    it('should reject recommendations without authentication', async () => {
      const requestData = {
        userPreferences: { budget: 500 },
        projectDescription: 'Test project'
      };

      const response = await request(testApp)
        .post('/api/v1/ai/recommendations/services')
        .send(requestData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('POST /api/v1/ai/recommendations/experts', () => {
    it('should get AI expert recommendations', async () => {
      const requestData = {
        projectRequirements: {
          skills: ['React', 'Node.js', 'Arabic'],
          category: 'tech-development',
          budget: 1000,
          timeline: '2 weeks',
          language: 'ar'
        },
        projectDescription: 'مشروع تطوير تطبيق ويب باللغة العربية',
        preferences: {
          location: 'Damascus',
          experienceLevel: 'senior',
          responseTime: 'fast'
        }
      };

      const response = await request(testApp)
        .post('/api/v1/ai/recommendations/experts')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(requestData)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          recommendations: expect.any(Array),
          reasoning: expect.any(String),
          totalExperts: expect.any(Number)
        }
      });

      if (response.body.data.recommendations.length > 0) {
        expect(response.body.data.recommendations[0]).toMatchObject({
          expert: expect.any(Object),
          matchScore: expect.any(Number),
          reasons: expect.any(Array),
          estimatedCost: expect.any(Number)
        });
      }
    });

    it('should reject expert recommendations without authentication', async () => {
      const requestData = {
        projectRequirements: { skills: ['React'] },
        projectDescription: 'Test project'
      };

      const response = await request(testApp)
        .post('/api/v1/ai/recommendations/experts')
        .send(requestData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('POST /api/v1/ai/analyze/project', () => {
    it('should analyze project requirements', async () => {
      const projectData = {
        description: 'أريد تطوير متجر إلكتروني لبيع المنتجات المحلية السورية مع دعم الدفع الإلكتروني',
        requirements: [
          'Product catalog',
          'Shopping cart',
          'Payment integration',
          'Arabic RTL support',
          'Mobile responsive'
        ],
        budget: 2000,
        timeline: '1 month',
        language: 'ar'
      };

      const response = await request(testApp)
        .post('/api/v1/ai/analyze/project')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .send(projectData)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          analysis: {
            complexity: expect.any(String),
            estimatedCost: expect.any(Object),
            timeline: expect.any(Object),
            requiredSkills: expect.any(Array),
            recommendations: expect.any(Array)
          },
          categories: expect.any(Array),
          suggestedExperts: expect.any(Array)
        }
      });
    });

    it('should reject project analysis without authentication', async () => {
      const projectData = {
        description: 'Test project',
        budget: 1000
      };

      const response = await request(testApp)
        .post('/api/v1/ai/analyze/project')
        .send(projectData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('GET /api/v1/ai/conversations', () => {
    it('should get user AI conversations', async () => {
      const response = await request(testApp)
        .get('/api/v1/ai/conversations')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          conversations: expect.any(Array)
        }
      });
    });

    it('should reject conversations request without authentication', async () => {
      const response = await request(testApp)
        .get('/api/v1/ai/conversations')
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });
});
