/**
 * AI Chat Service for Mobile App
 * Handles real-time AI conversation via WebSocket
 */

import { io, Socket } from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authStore } from '../store/authStore';
import { showToast } from '../utils/toast';

export interface ChatMessage {
  id: string;
  content: string;
  type: 'user' | 'ai';
  timestamp: Date;
  isTyping?: boolean;
}

export interface ConversationSession {
  sessionId: string;
  sessionType: 'onboarding' | 'profile_optimization' | 'service_creation';
  userRole: 'CLIENT' | 'EXPERT';
  language: 'ar' | 'en';
  currentStep: string;
  status: 'active' | 'completed' | 'abandoned' | 'paused';
  messages: ChatMessage[];
  extractedData: Record<string, any>;
  isCompleted: boolean;
}

export interface AIResponse {
  sessionId: string;
  userMessage: ChatMessage;
  aiMessage: ChatMessage;
  currentStep: string;
  extractedData: Record<string, any>;
  isCompleted: boolean;
}

class AIChatService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private currentSession: ConversationSession | null = null;
  private messageListeners: ((message: ChatMessage) => void)[] = [];
  private sessionListeners: ((session: ConversationSession) => void)[] = [];
  private typingListeners: ((isTyping: boolean) => void)[] = [];
  private errorListeners: ((error: string) => void)[] = [];

  /**
   * Initialize WebSocket connection
   */
  async connect(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem('accessToken');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const API_URL = __DEV__
        ? 'http://localhost:3005'
        : 'https://api.freela-syria.com';

      this.socket = io(API_URL, {
        auth: {
          token,
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000,
      });

      this.setupEventHandlers();

      return new Promise((resolve, reject) => {
        this.socket!.on('connect', () => {
          console.log('✅ AI Chat WebSocket connected');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          console.error('❌ AI Chat WebSocket connection error:', error);
          this.isConnected = false;
          reject(error);
        });

        // Set timeout for connection
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('Connection timeout'));
          }
        }, 10000);
      });

    } catch (error) {
      console.error('Failed to connect to AI Chat service:', error);
      throw error;
    }
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      console.log('🔌 AI Chat WebSocket disconnected');
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('disconnect', (reason) => {
      console.log('🔌 AI Chat WebSocket disconnected:', reason);
      this.isConnected = false;
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.reconnect();
      }
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`🔄 AI Chat WebSocket reconnected after ${attemptNumber} attempts`);
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('❌ AI Chat WebSocket reconnection error:', error);
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        showToast('error', 'فشل في الاتصال بالخادم', 'Connection to server failed');
        this.notifyError('Connection failed after multiple attempts');
      }
    });

    // AI Chat events
    this.socket.on('conversation_started', (data: {
      sessionId: string;
      currentStep: string;
      messages: any[];
      extractedData: Record<string, any>;
    }) => {
      console.log('🎯 AI Conversation started:', data.sessionId);
      
      this.currentSession = {
        sessionId: data.sessionId,
        sessionType: 'onboarding', // Default, will be updated
        userRole: authStore.getState().user?.role as 'CLIENT' | 'EXPERT' || 'CLIENT',
        language: 'ar', // Default, will be updated
        currentStep: data.currentStep,
        status: 'active',
        messages: data.messages.map(msg => ({
          id: `msg_${Date.now()}_${Math.random()}`,
          content: msg.content,
          type: msg.role === 'assistant' ? 'ai' : 'user',
          timestamp: new Date(msg.timestamp || Date.now()),
        })),
        extractedData: data.extractedData,
        isCompleted: false,
      };

      this.notifySessionUpdate(this.currentSession);
    });

    this.socket.on('session_joined', (data: {
      sessionId: string;
      currentStep: string;
      messages: any[];
      extractedData: Record<string, any>;
    }) => {
      console.log('🔗 AI Session joined:', data.sessionId);
      
      if (this.currentSession) {
        this.currentSession.currentStep = data.currentStep;
        this.currentSession.messages = data.messages.map(msg => ({
          id: `msg_${Date.now()}_${Math.random()}`,
          content: msg.content,
          type: msg.role === 'assistant' ? 'ai' : 'user',
          timestamp: new Date(msg.timestamp || Date.now()),
        }));
        this.currentSession.extractedData = data.extractedData;
        
        this.notifySessionUpdate(this.currentSession);
      }
    });

    this.socket.on('ai_message_response', (data: AIResponse) => {
      console.log('💬 AI Message response received');
      
      if (this.currentSession && data.sessionId === this.currentSession.sessionId) {
        // Add user message
        this.currentSession.messages.push(data.userMessage);
        this.notifyNewMessage(data.userMessage);
        
        // Add AI response
        this.currentSession.messages.push(data.aiMessage);
        this.notifyNewMessage(data.aiMessage);
        
        // Update session state
        this.currentSession.currentStep = data.currentStep;
        this.currentSession.extractedData = data.extractedData;
        this.currentSession.isCompleted = data.isCompleted;
        
        if (data.isCompleted) {
          this.currentSession.status = 'completed';
        }
        
        this.notifySessionUpdate(this.currentSession);
      }
    });

    this.socket.on('ai_typing', (data: { isTyping: boolean }) => {
      console.log('⌨️ AI typing indicator:', data.isTyping);
      this.notifyTyping(data.isTyping);
    });

    this.socket.on('user_sessions', (data: { sessions: any[] }) => {
      console.log('📋 User sessions received:', data.sessions.length);
      // Handle user sessions list if needed
    });

    this.socket.on('error', (data: { message: string; details?: string }) => {
      console.error('❌ AI Chat error:', data.message);
      showToast('error', 'خطأ في المحادثة', data.message);
      this.notifyError(data.message);
    });
  }

  /**
   * Start new AI conversation
   */
  async startConversation(params: {
    userRole: 'CLIENT' | 'EXPERT';
    language: 'ar' | 'en';
    sessionType?: 'onboarding' | 'profile_optimization' | 'service_creation';
  }): Promise<void> {
    if (!this.isConnected || !this.socket) {
      await this.connect();
    }

    console.log('🚀 Starting AI conversation:', params);
    
    this.socket!.emit('start_ai_conversation', {
      userRole: params.userRole,
      language: params.language,
      sessionType: params.sessionType || 'onboarding',
    });
  }

  /**
   * Join existing conversation session
   */
  async joinSession(sessionId: string): Promise<void> {
    if (!this.isConnected || !this.socket) {
      await this.connect();
    }

    console.log('🔗 Joining AI session:', sessionId);
    
    this.socket!.emit('join_ai_session', { sessionId });
  }

  /**
   * Send message to AI
   */
  async sendMessage(message: string): Promise<void> {
    if (!this.isConnected || !this.socket || !this.currentSession) {
      throw new Error('Not connected to AI chat or no active session');
    }

    if (!message.trim()) {
      throw new Error('Message cannot be empty');
    }

    console.log('📤 Sending message to AI:', message.substring(0, 50) + '...');
    
    this.socket.emit('send_ai_message', {
      sessionId: this.currentSession.sessionId,
      message: message.trim(),
    });
  }

  /**
   * Send typing indicator
   */
  sendTyping(isTyping: boolean): void {
    if (this.isConnected && this.socket && this.currentSession) {
      this.socket.emit('typing', {
        sessionId: this.currentSession.sessionId,
        isTyping,
      });
    }
  }

  /**
   * Get user's conversation sessions
   */
  getUserSessions(): void {
    if (this.isConnected && this.socket) {
      this.socket.emit('get_user_sessions');
    }
  }

  /**
   * Get current session
   */
  getCurrentSession(): ConversationSession | null {
    return this.currentSession;
  }

  /**
   * Check if connected
   */
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  /**
   * Event listeners
   */
  onMessage(callback: (message: ChatMessage) => void): () => void {
    this.messageListeners.push(callback);
    return () => {
      const index = this.messageListeners.indexOf(callback);
      if (index > -1) {
        this.messageListeners.splice(index, 1);
      }
    };
  }

  onSessionUpdate(callback: (session: ConversationSession) => void): () => void {
    this.sessionListeners.push(callback);
    return () => {
      const index = this.sessionListeners.indexOf(callback);
      if (index > -1) {
        this.sessionListeners.splice(index, 1);
      }
    };
  }

  onTyping(callback: (isTyping: boolean) => void): () => void {
    this.typingListeners.push(callback);
    return () => {
      const index = this.typingListeners.indexOf(callback);
      if (index > -1) {
        this.typingListeners.splice(index, 1);
      }
    };
  }

  onError(callback: (error: string) => void): () => void {
    this.errorListeners.push(callback);
    return () => {
      const index = this.errorListeners.indexOf(callback);
      if (index > -1) {
        this.errorListeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify listeners
   */
  private notifyNewMessage(message: ChatMessage): void {
    this.messageListeners.forEach(callback => callback(message));
  }

  private notifySessionUpdate(session: ConversationSession): void {
    this.sessionListeners.forEach(callback => callback(session));
  }

  private notifyTyping(isTyping: boolean): void {
    this.typingListeners.forEach(callback => callback(isTyping));
  }

  private notifyError(error: string): void {
    this.errorListeners.forEach(callback => callback(error));
  }

  /**
   * Reconnect logic
   */
  private async reconnect(): Promise<void> {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      try {
        await this.connect();
      } catch (error) {
        console.error('Reconnection failed:', error);
      }
    }
  }
}

// Export singleton instance
export const aiChatService = new AIChatService();
