/**
 * Authentication API Tests for Freela Syria
 * Tests all authentication endpoints including registration, login, token refresh, etc.
 */

import request from 'supertest';
import { app } from '../app';
import { DatabaseService } from '@freela/database';

describe('Authentication API', () => {
  let testApp: any;
  let testUser: any;
  let authTokens: any;

  beforeAll(async () => {
    // Initialize test app
    testApp = app;
  });

  afterAll(async () => {
    // Clean up test data
    if (testUser?.id) {
      try {
        // Note: In a real test environment, you'd clean up test data
        // For now, we'll skip cleanup to avoid affecting seeded data
      } catch (error) {
        console.log('Cleanup error (expected in test):', (error as Error).message);
      }
    }
  });

  describe('POST /api/v1/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: global.testUtils.generateRandomEmail(),
        password: 'TestPass123!',
        firstName: 'تست',
        lastName: 'يوزر',
        phone: global.testUtils.generateRandomPhone(),
        role: 'CLIENT',
        language: 'ar',
        acceptTerms: true
      };

      const response = await request(testApp)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Registration successful'),
        data: {
          user: {
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            role: userData.role,
            language: userData.language
          },
          verificationRequired: true
        }
      });

      testUser = response.body.data.user;
    });

    it('should reject registration with invalid email', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'TestPass123!',
        firstName: 'Test',
        lastName: 'User',
        phone: '+963-11-1234567',
        role: 'CLIENT',
        acceptTerms: true
      };

      const response = await request(testApp)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: 'Validation failed'
      });
    });

    it('should reject registration without accepting terms', async () => {
      const userData = {
        email: global.testUtils.generateRandomEmail(),
        password: 'TestPass123!',
        firstName: 'Test',
        lastName: 'User',
        phone: '+963-11-1234567',
        role: 'CLIENT',
        acceptTerms: false
      };

      const response = await request(testApp)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('terms')
      });
    });

    it('should reject duplicate email registration', async () => {
      const userData = {
        email: '<EMAIL>', // Existing seeded user
        password: 'TestPass123!',
        firstName: 'Test',
        lastName: 'User',
        phone: '+963-11-9999999',
        role: 'CLIENT',
        acceptTerms: true
      };

      const response = await request(testApp)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('already registered')
      });
    });
  });

  describe('POST /api/v1/auth/login', () => {
    it('should login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(testApp)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            email: loginData.email,
            firstName: expect.any(String),
            lastName: expect.any(String),
            role: expect.any(String)
          },
          tokens: {
            accessToken: expect.any(String),
            refreshToken: expect.any(String),
            expiresIn: expect.any(Number),
            refreshExpiresIn: expect.any(Number)
          }
        }
      });

      authTokens = response.body.data.tokens;
    });

    it('should reject login with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(testApp)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Invalid email or password')
      });
    });

    it('should reject login with invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await request(testApp)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Invalid email or password')
      });
    });
  });

  describe('POST /api/v1/auth/refresh', () => {
    it('should refresh tokens with valid refresh token', async () => {
      if (!authTokens?.refreshToken) {
        // Login first to get tokens
        const loginResponse = await request(testApp)
          .post('/api/v1/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'password123'
          });
        authTokens = loginResponse.body.data.tokens;
      }

      const response = await request(testApp)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken: authTokens.refreshToken })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Tokens refreshed successfully',
        data: {
          tokens: {
            accessToken: expect.any(String),
            refreshToken: expect.any(String),
            expiresIn: expect.any(Number),
            refreshExpiresIn: expect.any(Number)
          }
        }
      });
    });

    it('should reject refresh with invalid token', async () => {
      const response = await request(testApp)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Invalid or expired')
      });
    });
  });

  describe('GET /api/v1/auth/profile', () => {
    it('should get user profile with valid token', async () => {
      if (!authTokens?.accessToken) {
        // Login first to get tokens
        const loginResponse = await request(testApp)
          .post('/api/v1/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'password123'
          });
        authTokens = loginResponse.body.data.tokens;
      }

      const response = await request(testApp)
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          user: {
            email: '<EMAIL>',
            firstName: expect.any(String),
            lastName: expect.any(String),
            role: expect.any(String)
          }
        }
      });
    });

    it('should reject profile request without token', async () => {
      const response = await request(testApp)
        .get('/api/v1/auth/profile')
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        message: expect.stringContaining('Authentication required')
      });
    });
  });

  describe('POST /api/v1/auth/logout', () => {
    it('should logout successfully with valid token', async () => {
      if (!authTokens?.accessToken) {
        // Login first to get tokens
        const loginResponse = await request(testApp)
          .post('/api/v1/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'password123'
          });
        authTokens = loginResponse.body.data.tokens;
      }

      const response = await request(testApp)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${authTokens.accessToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Logout successful'
      });
    });
  });
});
